{"tests/test_models.py::TestTournamentModel::test_tournament_relationships": true, "tests/test_models.py::TestTeamModel::test_team_creation": true, "tests/test_models.py::TestTeamModel::test_team_relationships": true, "tests/test_models.py::TestMatchModel::test_match_creation": true, "tests/test_models.py::TestMatchModel::test_match_result_setting": true, "tests/test_models.py::TestMatchModel::test_match_relationships": true, "tests/test_models.py::TestModelValidation::test_unique_constraints": true, "tests/test_services.py::TestBracketGenerator::test_single_elimination_bracket_4_teams": true, "tests/test_auth.py::TestAuthRoutes::test_user_login": true, "tests/test_auth.py::TestAuthRoutes::test_user_logout": true, "tests/test_auth.py::TestAuthRoutes::test_registration_validation": true, "tests/test_auth.py::TestAuthRoutes::test_duplicate_username_registration": true, "tests/test_auth.py::TestAuthProtection::test_admin_routes_require_admin": true, "tests/test_auth.py::TestAuthProtection::test_organizer_routes_require_organizer": true, "tests/test_auth.py::TestUserRoles::test_admin_access": true, "tests/test_auth.py::TestUserRoles::test_organizer_access": true, "tests/test_auth.py::TestUserRoles::test_player_access": true, "tests/test_auth.py::TestSessionManagement::test_session_persistence": true, "tests/test_auth.py::TestSessionManagement::test_logout_clears_session": true, "tests/test_services.py::TestBracketGenerator::test_single_elimination_bracket_8_teams": true, "tests/test_services.py::TestBracketGenerator::test_round_robin_bracket": true, "tests/test_services.py::TestBracketGenerator::test_bracket_generation_insufficient_teams": true, "tests/test_services.py::TestMatchScheduler::test_schedule_matches_basic": true, "tests/test_services.py::TestMatchScheduler::test_schedule_matches_with_intervals": true, "tests/test_services.py::TestMatchScheduler::test_schedule_matches_different_rounds": true, "tests/test_services.py::TestLeaderboardService::test_calculate_single_elimination_leaderboard": true, "tests/test_services.py::TestLeaderboardService::test_calculate_round_robin_leaderboard": true, "tests/test_services.py::TestLeaderboardService::test_leaderboard_with_no_matches": true, "tests/test_tournaments.py::TestTournamentRoutes::test_tournament_create_page_authenticated": true, "tests/test_tournaments.py::TestTournamentCreation::test_create_tournament_success": true, "tests/test_tournaments.py::TestTournamentCreation::test_create_tournament_validation": true, "tests/test_tournaments.py::TestTournamentRegistration::test_team_registration_page": true, "tests/test_tournaments.py::TestTournamentRegistration::test_team_registration_success": true, "tests/test_tournaments.py::TestTournamentRegistration::test_duplicate_team_registration": true, "tests/test_tournaments.py::TestTournamentBrackets::test_bracket_generation": true, "tests/test_tournaments.py::TestTournamentBrackets::test_bracket_without_teams": true, "tests/test_tournaments.py::TestTournamentManagement::test_tournament_edit_page": true, "tests/test_tournaments.py::TestTournamentManagement::test_tournament_edit_success": true, "tests/test_tournaments.py::TestTournamentManagement::test_tournament_delete": true, "tests/test_tournaments.py::TestTournamentManagement::test_unauthorized_tournament_edit": true, "tests/test_tournaments.py::TestTournamentManagement::test_edit_other_users_tournament": true, "tests/test_tournaments.py::TestTournamentStatus::test_tournament_status_transitions": true, "tests/test_tournaments.py::TestTournamentStatus::test_tournament_start": true}