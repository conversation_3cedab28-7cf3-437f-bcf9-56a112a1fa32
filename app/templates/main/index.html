{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <!-- Hero Section -->
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <div class="container-fluid py-5">
                <h1 class="display-5 fw-bold">Welcome to Esports Tournament Manager</h1>
                <p class="col-md-8 fs-4">
                    Create, manage, and participate in competitive esports tournaments. 
                    Join the community of gamers and compete in your favorite games!
                </p>
                {% if not current_user.is_authenticated %}
                    <a class="btn btn-light btn-lg" href="{{ url_for('auth.register') }}" role="button">
                        Get Started <i class="fas fa-arrow-right"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                <h5 class="card-title">{{ tournament_count }}</h5>
                <p class="card-text">Active Tournaments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-info mb-2"></i>
                <h5 class="card-title">{{ team_count }}</h5>
                <p class="card-text">Registered Teams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-gamepad fa-2x text-success mb-2"></i>
                <h5 class="card-title">{{ player_count }}</h5>
                <p class="card-text">Active Players</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar fa-2x text-danger mb-2"></i>
                <h5 class="card-title">{{ match_count }}</h5>
                <p class="card-text">Matches Played</p>
            </div>
        </div>
    </div>
</div>

<!-- Featured Tournaments -->
<div class="row">
    <div class="col-lg-8">
        <h3><i class="fas fa-fire"></i> Featured Tournaments</h3>
        {% if featured_tournaments %}
            <div class="row">
                {% for tournament in featured_tournaments %}
                    <div class="col-md-6 mb-3">
                        <div class="card tournament-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">{{ tournament.name }}</h5>
                                <p class="card-text">{{ tournament.description[:100] }}...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ tournament.start_date.strftime('%b %d, %Y') }}
                                    </small>
                                    <span class="badge bg-{{ 'success' if tournament.status.value == 'registration_open' else 'secondary' }}">
                                        {{ tournament.status.value.replace('_', ' ').title() }}
                                    </span>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-users"></i> {{ tournament.teams|length }}/{{ tournament.max_teams }} teams
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="{{ url_for('tournaments.view', id=tournament.id) }}" class="btn btn-primary btn-sm">
                                    View Details
                                </a>
                                {% if current_user.is_authenticated and tournament.status.value == 'registration_open' %}
                                    <a href="{{ url_for('tournaments.register', id=tournament.id) }}" class="btn btn-outline-success btn-sm">
                                        Register Team
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No tournaments available at the moment. 
                {% if current_user.is_authenticated and current_user.role.value in ['organizer', 'admin'] %}
                    <a href="{{ url_for('tournaments.create') }}" class="alert-link">Create the first tournament!</a>
                {% endif %}
            </div>
        {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <h3><i class="fas fa-newspaper"></i> Latest News</h3>
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Platform Updates</h6>
                <p class="card-text">Welcome to the Esports Tournament Management Platform! Register now to start competing.</p>
                <small class="text-muted">Just now</small>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
            <div class="mt-4">
                <h5><i class="fas fa-user-circle"></i> Quick Actions</h5>
                <div class="list-group">
                    {% if current_user.role.value == 'player' %}
                        <a href="{{ url_for('teams.my_teams') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-users"></i> My Teams
                        </a>
                        <a href="{{ url_for('tournaments.my_tournaments') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-trophy"></i> My Tournaments
                        </a>
                    {% endif %}
                    {% if current_user.role.value in ['organizer', 'admin'] %}
                        <a href="{{ url_for('tournaments.create') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-plus"></i> Create Tournament
                        </a>
                        <a href="{{ url_for('tournaments.manage') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog"></i> Manage Tournaments
                        </a>
                    {% endif %}
                    <a href="{{ url_for('auth.profile') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-edit"></i> Edit Profile
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
